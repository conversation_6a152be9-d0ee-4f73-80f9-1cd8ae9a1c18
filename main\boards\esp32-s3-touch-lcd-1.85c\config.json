{
    "target": "esp32s3",
    "builds": [
        {
            "name": "esp32-s3-touch-lcd-1.85c",
            "sdkconfig_append": ["CONFIG_BT_ENABLED=y", "CONFIG_BT_NIMBLE_ENABLED=y", "CONFIG_BT_CONTROLLER_ENABLED=y", "CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL=y", "CONFIG_BT_NIMBLE_LOG_LEVEL_INFO=y", "CONFIG_BT_NIMBLE_LOG_LEVEL=1", "CONFIG_BT_NIMBLE_MAX_CONNECTIONS=3", "CONFIG_BT_NIMBLE_MAX_BONDS=3", "CONFIG_BT_NIMBLE_MAX_CCCDS=8", "CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM=0", "CONFIG_BT_NIMBLE_PINNED_TO_CORE_0=y", "CONFIG_BT_NIMBLE_PINNED_TO_CORE=0", "CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE=4096", "CONFIG_BT_NIMBLE_ROLE_CENTRAL=y", "CONFIG_BT_NIMBLE_ROLE_PERIPHERAL=y", "CONFIG_BT_NIMBLE_ROLE_BROADCASTER=y", "CONFIG_BT_NIMBLE_ROLE_OBSERVER=y", "CONFIG_BT_NIMBLE_GATT_CLIENT=y", "CONFIG_BT_NIMBLE_GATT_SERVER=y", "CONFIG_BT_NIMBLE_SECURITY_ENABLE=y", "CONFIG_BT_NIMBLE_SM_LEGACY=y", "CONFIG_BT_NIMBLE_SM_SC=y", "CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION=y", "CONFIG_BT_NIMBLE_SM_LVL=0", "CONFIG_BT_NIMBLE_SM_SC_ONLY=0", "CONFIG_BT_NIMBLE_PRINT_ERR_NAME=y", "CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME="nimble"", "CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN=31", "CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU=256", "CONFIG_BT_NIMBLE_ATT_MAX_PREP_ENTRIES=64", "CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE=0", "CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT=12", "CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE=256", "CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT=24", "CONFIG_BT_NIMBLE_MSYS_2_BLOCK_SIZE=320", "CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT=24", "CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE=255", "CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE=70", "CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT=30", "CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT=8", "CONFIG_BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT=1", "CONFIG_BT_NIMBLE_GATT_MAX_PROCS=4", "CONFIG_BT_NIMBLE_RPA_TIMEOUT=900", "CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS=y", "CONFIG_BT_NIMBLE_HS_STOP_TIMEOUT_MS=2000", "CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=y", "CONFIG_BT_NIMBLE_MAX_CONN_REATTEMPT=3", "CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=y", "CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY=y", "CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY=y", "CONFIG_BT_NIMBLE_EXT_SCAN=y", "CONFIG_BT_NIMBLE_ENABLE_PERIODIC_SYNC=y", "CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS=0", "CONFIG_BT_NIMBLE_WHITELIST_SIZE=12", "CONFIG_BT_NIMBLE_USE_ESP_TIMER=y", "CONFIG_BT_NIMBLE_LEGACY_VHCI_ENABLE=y", "CONFIG_BT_NIMBLE_PROX_SERVICE=y", "CONFIG_BT_NIMBLE_ANS_SERVICE=y", "CONFIG_BT_NIMBLE_CTS_SERVICE=y", "CONFIG_BT_NIMBLE_HTP_SERVICE=y", "CONFIG_BT_NIMBLE_IPSS_SERVICE=y", "CONFIG_BT_NIMBLE_TPS_SERVICE=y", "CONFIG_BT_NIMBLE_IAS_SERVICE=y", "CONFIG_BT_NIMBLE_LLS_SERVICE=y", "CONFIG_BT_NIMBLE_SPS_SERVICE=y", "CONFIG_BT_NIMBLE_HR_SERVICE=y", "CONFIG_BT_NIMBLE_BAS_SERVICE=y", "CONFIG_BT_NIMBLE_DIS_SERVICE=y", "CONFIG_BT_NIMBLE_GAP_SERVICE=y", "CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM=0", "CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC=0", "CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN=0", "CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR=0", "CONFIG_BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP=y", "CONFIG_BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION=-1", , "CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL=0", "CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL=0", "CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY=0", "CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO=0", "CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM=0", "CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC=0", "CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN=0", "CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR=0", , "CONFIG_UART_HW_FLOWCTRL_DISABLE=y", "CONFIG_BT_NIMBLE_HCI_UART_FLOW_CTRL=0", "CONFIG_BT_NIMBLE_HCI_UART_RTS_PIN=19", "CONFIG_BT_NIMBLE_HCI_UART_CTS_PIN=23", "CONFIG_BT_NIMBLE_EATT_CHAN_NUM=0", "CONFIG_BT_CTRL_MODE_EFF=1", "CONFIG_BT_CTRL_BLE_MAX_ACT=6", "CONFIG_BT_CTRL_BLE_MAX_ACT_EFF=6", "CONFIG_BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB=0", "CONFIG_BT_CTRL_PINNED_TO_CORE_0=y", "CONFIG_BT_CTRL_PINNED_TO_CORE=0", "CONFIG_BT_CTRL_HCI_MODE_VHCI=y", "CONFIG_BT_CTRL_HCI_TL=1", "CONFIG_BT_CTRL_ADV_DUP_FILT_MAX=30", "CONFIG_BT_BLE_CCA_MODE_NONE=y", "CONFIG_BT_BLE_CCA_MODE=0", "CONFIG_BT_CTRL_HW_CCA_VAL=20", "CONFIG_BT_CTRL_HW_CCA_EFF=0", "CONFIG_BT_CTRL_CE_LENGTH_TYPE_ORIG=y", "CONFIG_BT_CTRL_CE_LENGTH_TYPE_EFF=0", "CONFIG_BT_CTRL_TX_ANTENNA_INDEX_0=y", "CONFIG_BT_CTRL_TX_ANTENNA_INDEX_EFF=0", "CONFIG_BT_CTRL_RX_ANTENNA_INDEX_0=y", "CONFIG_BT_CTRL_RX_ANTENNA_INDEX_EFF=0", "CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_P9=y", "CONFIG_BT_CTRL_DFT_TX_POWER_LEVEL_EFF=11", "CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP=y", "CONFIG_BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM=100", "CONFIG_BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD=20", "CONFIG_BT_CTRL_BLE_SCAN_DUPL=y", "CONFIG_BT_CTRL_SCAN_DUPL_TYPE_DEVICE=y", "CONFIG_BT_CTRL_SCAN_DUPL_TYPE=0", "CONFIG_BT_CTRL_SCAN_DUPL_CACHE_SIZE=100", "CONFIG_BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD=0", "CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS=y", "CONFIG_BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EFF=0", , "CONFIG_BT_CTRL_SLEEP_MODE_EFF=0", "CONFIG_BT_CTRL_SLEEP_CLOCK_EFF=0", "CONFIG_BT_CTRL_HCI_TL_EFF=1", "CONFIG_BT_CTRL_CHAN_ASS_EN=y", "CONFIG_BT_CTRL_LE_PING_EN=y", , "CONFIG_BT_CTRL_DTM_ENABLE=y", "CONFIG_BT_CTRL_BLE_MASTER=y", "CONFIG_BT_CTRL_BLE_SCAN=y", "CONFIG_BT_CTRL_BLE_SECURITY_ENABLE=y", "CONFIG_BT_CTRL_BLE_ADV=y", "CONFIG_BT_ALARM_MAX_NUM=50"]
        }
    ]
}